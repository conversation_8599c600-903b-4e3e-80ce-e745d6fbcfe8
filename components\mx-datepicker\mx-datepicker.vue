<template>
	<view v-if="isShow" class="picker">
		<!-- 日期选择器 -->
		<view v-if="props.type!='time'" class="picker-modal">
			<view class="picker-modal-header">
				<view class="picker-icon picker-icon-zuozuo" :hover-stay-time="100" hover-class="picker-icon-active" @click="onSetYear('-1')"></view>
				<view class="picker-icon picker-icon-zuo" :hover-stay-time="100" hover-class="picker-icon-active" @click="onSetMonth('-1')"></view>
				<text class="picker-modal-header-title">{{title}}</text>
				<view class="picker-icon picker-icon-you" :hover-stay-time="100" hover-class="picker-icon-active" @click="onSetMonth('+1')"></view>
				<view class="picker-icon picker-icon-youyou" :hover-stay-time="100" hover-class="picker-icon-active" @click="onSetYear('+1')"></view>
			</view>
			<swiper class="picker-modal-body" :circular="true" :duration="200" :skip-hidden-item-layout="true" :current="calendarIndex" @change="onSwiperChange">
				<swiper-item class="picker-calendar" v-for="(calendar,calendarIndex2) in calendars" :key="calendarIndex2">
					<view class="picker-calendar-view" v-for="(week,index) in weeks" :key="index - 7">
						<view class="picker-calendar-view-item">{{week}}</view>
					</view>
					<view class="picker-calendar-view" v-for="(date,dateIndex) in calendar" :key="dateIndex" @click="onSelectDate(date)">
						<!-- 背景样式 -->
						<view v-show="date.bgStyle.type" :class="'picker-calendar-view-'+date.bgStyle.type" :style="{background: date.bgStyle.background}"></view>
						<!-- 正常和选中样式 -->
						<view class="picker-calendar-view-item" :style="{opacity: date.statusStyle.opacity, color: date.statusStyle.color, background: date.statusStyle.background}">
							<text>{{date.title}}</text>
						</view>
						<!-- 小圆点样式 -->
						<view class="picker-calendar-view-dot" :style="{opacity: date.dotStyle.opacity, background: date.dotStyle.background}"></view>
						<!-- 信息样式 -->
						<view v-show="date.tips" class="picker-calendar-view-tips">{{date.tips}}</view>
					</view>
				</swiper-item>
			</swiper>
			<view class="picker-modal-footer">
				<view class="picker-modal-footer-info">
					<block v-if="isMultiSelect">
						<view class="picker-display">
							<text>{{props.beginText}}日期</text>
							<text class="picker-display-text">{{BeginTitle}}</text>
							<view v-if="isContainTime" class="picker-display-link" :hover-stay-time="100" hover-class="picker-display-link-active"
							 :style="{color: props.color}" @click="onShowTimePicker('begin')">{{BeginTimeTitle}}</view>
						</view>
						<view class="picker-display">
							<text>{{props.endText}}日期</text>
							<text class="picker-display-text">{{EndTitle}}</text>
							<view v-if="isContainTime" class="picker-display-link" :hover-stay-time="100" hover-class="picker-display-link-active"
							 :style="{color: props.color}" @click="onShowTimePicker('end')">{{EndTimeTitle}}</view>
						</view>
					</block>
					<block v-else>
						<view class="picker-display">
							<text>当前选择</text>
							<text class="picker-display-text">{{BeginTitle}}</text>
							<view v-if="isContainTime" class="picker-display-link" :hover-stay-time="100" hover-class="picker-display-link-active"
							 :style="{color: props.color}" @click="onShowTimePicker('begin')">{{BeginTimeTitle}}</view>
						</view>
					</block>
				</view>
				<view class="picker-modal-footer-btn">
					<view class="picker-btn" :hover-stay-time="100" hover-class="picker-btn-active" @click="onCancel">取消</view>
					<view class="picker-btn" :style="{color: props.color}" :hover-stay-time="100" hover-class="picker-btn-active" @click="onConfirm">确定</view>
				</view>
			</view>
		</view>
		<!-- 时间选择器 -->
		<view v-if="showTimePicker" class="picker">
			<view class="picker-modal picker-time">
				<view class="picker-modal-header">
					<text class="picker-modal-header-title">选择日期</text>
				</view>
				<picker-view class="picker-modal-time" indicator-class="picker-modal-time-item" :value="timeValue" @change="onTimeChange">
					<picker-view-column>
						<view v-for="(v,i) in 24" :key="i">{{i<10?'0'+i:i}}时</view>
					</picker-view-column>
					<picker-view-column>
						<view v-for="(v,i) in 60" :key="i">{{i<10?'0'+i:i}}分</view>
					</picker-view-column>
					<picker-view-column v-if="props.showSeconds">
						<view v-for="(v,i) in 60" :key="i">{{i<10?'0'+i:i}}秒</view>
					</picker-view-column>
				</picker-view>
				<view class="picker-modal-footer">
					<view class="picker-modal-footer-info">
						<view class="picker-display">
							<text>当前选择</text>
							<text class="picker-display-text">{{PickerTimeTitle}}</text>
						</view>
					</view>
					<view class="picker-modal-footer-btn">
						<view class="picker-btn" :hover-stay-time="100" hover-class="picker-btn-active" @click="onCancelTime">取消</view>
						<view class="picker-btn" :style="{color}" :hover-stay-time="100" hover-class="picker-btn-active" @click="onConfirmTime">确定</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script lang="ts" setup>
import { ref, computed, watch, defineProps, defineEmits } from 'vue'

/**
 * 工具函数库
 */
const DateTools = {
	/**
	 * 获取公历节日
	 * @param date Date对象
	 */
	getHoliday(date) {
		let holidays = {
			'0101': '元旦',
			'0214': '情人',
			'0308': '妇女',
			'0312': '植树',
			'0401': '愚人',
			'0501': '劳动',
			'0504': '青年',
			'0601': '儿童',
			'0701': '建党',
			'0801': '建军',
			'0903': '抗日',
			'0910': '教师',
			'1001': '国庆',
			'1031': '万圣',
			'1224': '平安',
			'1225': '圣诞'
		};
		let value = this.format(date, 'mmdd');
		if (holidays[value]) return holidays[value];
		return false;
	},
	/**
	 * 解析标准日期格式
	 * @param s 日期字符串
	 * @return 返回Date对象
	 */
	parse: s => new Date(s.replace(/(年|月|-)/g, '/').replace(/(日)/g, '')),
	/**
	 * 比较日期是否为同一天
	 * @param a Date对象
	 * @param b Date对象
	 * @return Boolean
	 */
	isSameDay: (a, b) => a.getMonth() == b.getMonth() && a.getFullYear() == b.getFullYear() && a.getDate() == b.getDate(),
	/**
	 * 格式化Date对象
	 * @param d 日期对象
	 * @param f 格式字符串
	 * @return 返回格式化后的字符串
	 */
	format(d, f) {
		var o = {
			"m+": d.getMonth() + 1,
			"d+": d.getDate(),
			"h+": d.getHours(),
			"i+": d.getMinutes(),
			"s+": d.getSeconds(),
			"q+": Math.floor((d.getMonth() + 3) / 3),
		};
		if (/(y+)/.test(f))
			f = f.replace(RegExp.$1, (d.getFullYear() + "").substr(4 - RegExp.$1.length));
		for (var k in o)
			if (new RegExp("(" + k + ")").test(f))
				f = f.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
		return f;
	},
	/**
	 * 用于format格式化后的反解析
	 * @param s 日期字符串
	 * @param f 格式字符串
	 * @return 返回Date对象
	 */
	inverse(s, f) {
		var o = {
			"y": '',
			"m": '',
			"d": '',
			"h": '',
			"i": '',
			"s": '',
		};
		let d = new Date();
		if (s.length != f.length) return d;
		for (let i in f)
			if (o[f[i]] != undefined) o[f[i]] += s[i];
		if (o.y) d.setFullYear(o.y.length < 4 ? (d.getFullYear() + '').substr(0, 4 - o.y.length) + o.y : o.y);
		o.m && d.setMonth(o.m - 1, 1);
		o.d && d.setDate(o.d - 0);
		o.h && d.setHours(o.h - 0);
		o.i && d.setMinutes(o.i - 0);
		o.s && d.setSeconds(o.s - 0);
		return d;
	},
	/**
	 * 获取日历数组（42天）
	 * @param date 日期对象或日期字符串
	 * @param proc 处理日历(和forEach类似)，传递一个数组中的item
	 * @return Array
	 */
	getCalendar(date, proc) {
		let it = new Date(date),
			calendars = [];
		it.setDate(1);
		it.setDate(it.getDate() - ((it.getDay() == 0 ? 7 : it.getDay()) - 1)); //偏移量
		for (let i = 0; i < 42; i++) {
			let tmp = {
				dateObj: new Date(it),
				title: it.getDate(),
				isOtherMonth: it.getMonth() < date.getMonth() || it.getMonth() > date.getMonth()
			};
			calendars.push(Object.assign(tmp, proc ? proc(tmp) : {}));
			it.setDate(it.getDate() + 1);
		}
		return calendars;
	},
	/**
	 * 获取日期到指定的月份1号(不改变原来的date对象)
	 * @param d Date对象
	 * @param v 指定的月份
	 * @return Date对象
	 */
	getDateToMonth(d, v) {
		let n = new Date(d);
		n.setMonth(v, 1);
		return n;
	},
	/**
	 * 把时间数组转为时间字符串
	 * @param t Array[时,分,秒]
	 * @param showSecinds 是否显示秒
	 * @return 字符串 时:分[:秒]
	 */
	formatTimeArray(t, s) {
		let r = [...t];
		if (!s) r.length = 2;
		r.forEach((v, k) => r[k] = ('0' + v).slice(-2));
		return r.join(':');
	}
};

// Props 定义
const props = defineProps({
	//颜色
	color: {
		type: String,
		default: '#409eff'
	},
	//是否显示秒 针对type为datetime或time时生效
	showSeconds: {
		type: Boolean,
		default: false
	},
	//初始的值
	value: [String, Array],
	//类型date time datetime range rangetime
	type: {
		type: String,
		default: 'range'
	},
	//是否显示
	show: {
		type: Boolean,
		default: false
	},
	//初始格式
	format: {
		type: String,
		default: ''
	},
	//显示公历节日
	showHoliday: {
		type: Boolean,
		default: true
	},
	//显示提示
	showTips: {
		type: Boolean,
		default: false
	},
	//开始文案 针对type为范围选择时生效
	beginText: {
		type: String,
		default: '开始'
	},
	//结束文案 针对type为范围选择时生效
	endText: {
		type: String,
		default: '结束'
	}
})

// Emits 定义
const emit = defineEmits(['cancel', 'confirm'])

// 响应式数据
const isShow = ref(false) //是否显示
const isMultiSelect = ref(false) //是否为多选
const isContainTime = ref(false) //是否包含时间
const date = ref({}) //当前日期对象
const weeks = ref(["一", "二", "三", "四", "五", "六", "日"])
const title = ref('初始化') //标题
const calendars = ref([[],[],[]]) //日历数组
const calendarIndex = ref(1) //当前日历索引
const checkeds = ref([]) //选中的日期对象集合
const showTimePicker = ref(false) //是否显示时间选择器
const timeValue = ref([0, 0, 0]) //时间选择器的值
const timeType = ref('begin') //当前时间选择的类型
const beginTime = ref([0, 0, 0]) //当前所选的开始时间值
const endTime = ref([0, 0, 0]) //当前所选的结束时间值
// 方法定义
//设置值
const setValue = (value) => {
	date.value = new Date();
	checkeds.value = [];
	isMultiSelect.value = props.type.indexOf('range') >= 0;
	isContainTime.value = props.type.indexOf('time') >= 0;
	//将字符串解析为Date对象
	let parseDateStr = (str) => (props.format ? DateTools.inverse(str, props.format) : DateTools.parse(str));
	if (value) {
		if (isMultiSelect.value) {
			Array.isArray(value) && value.forEach((dateStr, index) => {
				let dateObj = parseDateStr(dateStr);
				let time = [dateObj.getHours(), dateObj.getMinutes(), dateObj.getSeconds()];
				if (index == 0) beginTime.value = time;
				else endTime.value = time;
				checkeds.value.push(dateObj);
			});
		} else {
			if (props.type == 'time') {
				let dateObj = parseDateStr('2019/1/1 ' + value);
				beginTime.value = [dateObj.getHours(), dateObj.getMinutes(), dateObj.getSeconds()];
				onShowTimePicker('begin');
			} else {
				checkeds.value.push(parseDateStr(value));
				if (isContainTime.value) beginTime.value = [
					checkeds.value[0].getHours(),
					checkeds.value[0].getMinutes(),
					checkeds.value[0].getSeconds()
				];
			}
		}
		if (checkeds.value.length) date.value = new Date(checkeds.value[0]);
	} else {
		if (isContainTime.value) {
			beginTime.value = [date.value.getHours(), date.value.getMinutes(), date.value.getSeconds()];
			if (isMultiSelect.value) endTime.value = [...beginTime.value];
		}
		checkeds.value.push(new Date(date.value));
	}
	if (props.type != 'time') refreshCalendars(true);
	else onShowTimePicker('begin');
}

//改变年份
const onSetYear = (value) => {
	date.value.setFullYear(date.value.getFullYear() + parseInt(value));
	refreshCalendars(true);
}

//改变月份
const onSetMonth = (value) => {
	date.value.setMonth(date.value.getMonth() + parseInt(value));
	refreshCalendars(true);
}

//时间选择变更
const onTimeChange = (e) => {
	timeValue.value = e.detail.value;
}

//设置时间选择器的显示状态
const onShowTimePicker = (type) => {
	showTimePicker.value = true;
	timeType.value = type;
	timeValue.value = type == 'begin' ? [...beginTime.value] : [...endTime.value];
}
//处理日历
const procCalendar = (item) => {
	//定义初始样式
	item.statusStyle = {
		opacity: 1,
		color: item.isOtherMonth ? '#ddd' : '#000',
		background: 'transparent'
	};
	item.bgStyle = {
		type: '',
		background: 'transparent'
	};
	item.dotStyle = {
		opacity: 1,
		background: 'transparent'
	};
	item.tips = "";
	//标记今天的日期
	if (DateTools.isSameDay(new Date(), item.dateObj)) {
		item.statusStyle.color = props.color;
		if (item.isOtherMonth) item.statusStyle.opacity = 0.3;
	}
	//标记选中项
	checkeds.value.forEach(dateObj => {
		if (DateTools.isSameDay(dateObj, item.dateObj)) {
			item.statusStyle.background = props.color;
			item.statusStyle.color = '#fff';
			item.statusStyle.opacity = 1;
			if (isMultiSelect.value && props.showTips) item.tips = props.beginText;
		}
	});
	//节假日或今日的日期标点
	if (item.statusStyle.background != props.color) {
		let holiday = props.showHoliday ? DateTools.getHoliday(item.dateObj) : false;
		if (holiday || DateTools.isSameDay(new Date(), item.dateObj)) {
			item.title = holiday || item.title;
			item.dotStyle.background = props.color;
			if (item.isOtherMonth) item.dotStyle.opacity = 0.2;
		}
	} else {
		item.title = item.dateObj.getDate();
	}
	//有两个日期
	if (checkeds.value.length == 2) {
		if (DateTools.isSameDay(checkeds.value[0], item.dateObj)) { //开始日期
			item.bgStyle.type = 'bgbegin';
		}
		if (DateTools.isSameDay(checkeds.value[1], item.dateObj)) { //结束日期
			if (isMultiSelect.value && props.showTips) item.tips = item.bgStyle.type ? props.beginText + ' / ' + props.endText : props.endText;
			if (!item.bgStyle.type) { //开始日期不等于结束日期
				item.bgStyle.type = 'bgend';
			} else {
				item.bgStyle.type = '';
			}
		}
		if (!item.bgStyle.type && (+item.dateObj > +checkeds.value[0] && +item.dateObj < +checkeds.value[1])) { //中间的日期
			item.bgStyle.type = 'bg';
			item.statusStyle.color = props.color;
		}
		if (item.bgStyle.type) {
			item.bgStyle.background = props.color;
			item.dotStyle.opacity = 1;
			item.statusStyle.opacity = 1;
		}
	}
}
//刷新日历
const refreshCalendars = (refresh = false) => {
	let dateObj = new Date(date.value);
	let before = DateTools.getDateToMonth(dateObj, dateObj.getMonth() - 1);
	let after = DateTools.getDateToMonth(dateObj, dateObj.getMonth() + 1);
	if (calendarIndex.value == 0) {
		if(refresh) calendars.value.splice(0, 1, DateTools.getCalendar(dateObj, procCalendar));
		calendars.value.splice(1, 1, DateTools.getCalendar(after, procCalendar));
		calendars.value.splice(2, 1, DateTools.getCalendar(before, procCalendar));
	} else if (calendarIndex.value == 1) {
		calendars.value.splice(0, 1, DateTools.getCalendar(before, procCalendar));
		if(refresh) calendars.value.splice(1, 1, DateTools.getCalendar(dateObj, procCalendar));
		calendars.value.splice(2, 1, DateTools.getCalendar(after, procCalendar));
	} else if (calendarIndex.value == 2) {
		calendars.value.splice(0, 1, DateTools.getCalendar(after, procCalendar));
		calendars.value.splice(1, 1, DateTools.getCalendar(before, procCalendar));
		if(refresh) calendars.value.splice(2, 1, DateTools.getCalendar(dateObj, procCalendar));
	}
	title.value = DateTools.format(date.value, 'yyyy年mm月');
}

//滑块切换
const onSwiperChange = (e) => {
	calendarIndex.value = e.detail.current;
	let calendar = calendars.value[calendarIndex.value];
	date.value = new Date(calendar[22].dateObj); //取中间一天，保证是当前的月份
	refreshCalendars();
}

//选中日期
const onSelectDate = (dateObj) => {
	if (~props.type.indexOf('range') && checkeds.value.length == 2) checkeds.value = [];
	else if (!(~props.type.indexOf('range')) && checkeds.value.length) checkeds.value = [];
	checkeds.value.push(new Date(dateObj.dateObj));
	checkeds.value.sort((a, b) => a - b); //从小到大排序
	calendars.value.forEach(calendar => {
		calendar.forEach(procCalendar); //重新处理
	});
}

//时间选择取消
const onCancelTime = () => {
	showTimePicker.value = false;
	props.type == 'time' && onCancel();
}

//时间选择确定
const onConfirmTime = () => {
	if (timeType.value == 'begin') beginTime.value = timeValue.value;
	else endTime.value = timeValue.value;
	showTimePicker.value = false;
	props.type == 'time' && onConfirm();
}

//取消
const onCancel = () => {
	emit('cancel', false);
}
//确定
const onConfirm = () => {
	let result = {
		value: null,
		date: null
	};
	//定义默认格式
	let defaultFormat = {
		'date': 'yyyy/mm/dd',
		'time': 'hh:ii' + (props.showSeconds ? ':ss' : ''),
		'datetime': ''
	};
	defaultFormat['datetime'] = defaultFormat.date + ' ' + defaultFormat.time;
	let fillTime = (dateObj, timeArr) => {
		dateObj.setHours(timeArr[0], timeArr[1]);
		if (props.showSeconds) dateObj.setSeconds(timeArr[2]);
	};
	if (props.type == 'time') {
		let dateObj = new Date();
		fillTime(dateObj, beginTime.value);
		result.value = DateTools.format(dateObj, props.format ? props.format : defaultFormat.time);
		result.date = dateObj;
	} else {
		if (isMultiSelect.value) {
			let values = [],
				dates = [];
			if (checkeds.value.length < 2) return uni.showToast({
				icon: 'none',
				title: '请选择两个日期'
			});
			checkeds.value.forEach((dateObj, index) => {
				let newDate = new Date(dateObj);
				if (isContainTime.value) {
					let time = [beginTime.value, endTime.value];
					fillTime(newDate, time[index]);
				}
				values.push(DateTools.format(newDate, props.format ? props.format : defaultFormat[isContainTime.value ?
					'datetime' : 'date']));
				dates.push(newDate);
			});
			result.value = values;
			result.date = dates;
		} else {
			let newDate = new Date(checkeds.value[0]);
			if (isContainTime.value) {
				newDate.setHours(beginTime.value[0], beginTime.value[1]);
				if (props.showSeconds) newDate.setSeconds(beginTime.value[2]);
			}
			result.value = DateTools.format(newDate, props.format ? props.format : defaultFormat[isContainTime.value ?
				'datetime' : 'date']);
			result.date = newDate;
		}
	}
	emit('confirm', result);
}
// 计算属性
const BeginTitle = computed(() => {
	let value = '未选择';
	if (checkeds.value.length) value = DateTools.format(checkeds.value[0], 'yy/mm/dd');
	return value;
})

const EndTitle = computed(() => {
	let value = '未选择';
	if (checkeds.value.length == 2) value = DateTools.format(checkeds.value[1], 'yy/mm/dd');
	return value;
})

const PickerTimeTitle = computed(() => {
	return DateTools.formatTimeArray(timeValue.value, props.showSeconds);
})

const BeginTimeTitle = computed(() => {
	return BeginTitle.value != '未选择' ? DateTools.formatTimeArray(beginTime.value, props.showSeconds) : '';
})

const EndTimeTitle = computed(() => {
	return EndTitle.value != '未选择' ? DateTools.formatTimeArray(endTime.value, props.showSeconds) : '';
})

// 监听器
watch(() => props.show, (newValue, oldValue) => {
	newValue && setValue(props.value);
	isShow.value = newValue;
})

watch(() => props.value, (newValue, oldValue) => {
	setTimeout(()=>{
		setValue(newValue);
	}, 0);
})
</script>

<style lang="scss" scoped>
@use 'sass:math';

$z-index: 100;
$cell-spacing: 20upx;
$calendar-size: 630upx;
$calendar-item-size: 90upx;

	.picker {
		position: fixed;
		z-index: $z-index;
		background: rgba(255, 255, 255, 0);
		left: 0;
		top: 0;
		width: 100%;
		height: 100%;
		font-size: 28upx;

		&-btn {
			padding: $cell-spacing*0.5 $cell-spacing;
			border-radius: 12upx;
			color: #666;

			&-active {
				background: rgba(0, 0, 0, .1);
			}
		}

		&-display {
			color: #666;

			&-text {
				color: #000;
				margin: 0 $cell-spacing*0.5;
			}

			&-link {
				display: inline-block;

				&-active {
					background: rgba(0, 0, 0, .1);
				}
			}
		}

		&-time {
			width: $calendar-size - 80upx !important;
			left: (math.div(750upx - $calendar-size, 2) + 40upx) !important;
		}

		&-modal {
			background: #fff;
			position: absolute;
			top: 50%;
			left: math.div(750upx - $calendar-size, 2);
			width: $calendar-size;
			transform: translateY(-50%);
			box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.1);
			border-radius: 12upx;

			&-header {
				text-align: center;
				line-height: 80upx;
				font-size: 32upx;

				&-title {
					display: inline-block;
					width: 40%;
				}

				.picker-icon {
					display: inline-block;
					line-height: 50upx;
					width: 50upx;
					height: 50upx;
					border-radius: 50upx;
					text-align: center;
					margin: 10upx;
					background: #fff;
					font-size: 36upx;

					&-active {
						background: rgba(0, 0, 0, .1);
					}
				}
			}

			&-body {
				width: $calendar-size !important;
				height: $calendar-size !important;
				position: relative;
			}

			&-time {
				width: 100%;
				height: 180upx;
				text-align: center;
				line-height: 60upx;
			}

			&-footer {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: $cell-spacing;

				&-info {
					flex-grow: 1;
				}

				&-btn {
					flex-shrink: 0;
					display: flex;
				}
			}
		}

		&-calendar {
			position: absolute;
			left: 0;
			top: 0;
			width: 100%;
			height: 100%;
			display: flex;
			align-items: center;
			flex-wrap: wrap;

			&-view {
				position: relative;
				width: $calendar-item-size;
				height: $calendar-item-size;
				text-align: center;

				&-bgbegin,
				&-bg,
				&-bgend,
				&-item,
				&-dot,
				&-tips {
					position: absolute;
					transition: .2s;
				}

				&-bgbegin,
				&-bg,
				&-bgend {
					opacity: .15;
					height: 80%;
				}

				&-bg {
					left: 0;
					top: 10%;
					width: 100%;
				}

				&-bgbegin {
					border-radius: $calendar-item-size 0 0 $calendar-item-size;
					top: 10%;
					left: 10%;
					width: 90%;
				}

				&-bgend {
					border-radius: 0 $calendar-item-size $calendar-item-size 0;
					top: 10%;
					left: 0%;
					width: 90%;
				}

				&-item {
					left: 5%;
					top: 5%;
					width: 90%;
					height: 90%;
					border-radius: $calendar-item-size;
					display: flex;
					align-items: center;
					justify-content: center;
				}

				&-dot {
					right: 10%;
					top: 10%;
					width: 12upx;
					height: 12upx;
					border-radius: 12upx;
				}

				&-tips {
					bottom: 100%;
					left: 50%;
					transform: translateX(-50%);
					background: #4E4B46;
					color: #fff;
					border-radius: 12upx;
					padding: 10upx 20upx;
					font-size: 24upx;
					width: max-content;
					margin-bottom: 5px;
					pointer-events: none;

					&:after {
						content: "";
						position: absolute;
						top: 100%;
						left: 50%;
						transform: translateX(-50%);
						width: 0;
						height: 0;
						border-style: solid;
						border-width: 5px 5px 0 5px;
						border-color: #4E4B46 transparent transparent transparent;
					}
				}
			}
		}
	}

	@font-face {
		font-family: "mxdatepickericon";
		src: url('data:application/x-font-woff2;charset=utf-8;base64,d09GMgABAAAAAAMYAAsAAAAACBgAAALMAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHEIGVgCDIgqDRIJiATYCJAMUCwwABCAFhG0HSRvfBsg+QCa3noNAyAQ9w6GDvbwpNp2vloCyn8bD/x+y+/5qDhtj+T4eRVEcbsCoKMFASzCgLdDkmqYDwgxkWQ6YH5L/YnppOlLEjlnter43YRjU7M6vJ3iGADVAgJn5kqjv/wEii23T86UsAQT+04fV+o97VTMx4PPZt4DlorLXwIQiGMA5uhaVrBWqGHfQXcTEiE+PE+g2SUlxWlLVBHwUYFMgrgwSB3wstTKSGzqF1nOyiGeeOtNjV4An/vvxR58PSc3AzrMViyDvPo/7dVEUzn5GROfIWAcU4rLXfMFdhte56y4We9gGNEVIezkBOOaQXUrbTf/hJVkhGpDdCw7dSOEzByMEn3kIic98hMxnAfeFPKWCbjRcA148/HxhCEkaA94eGWFaGolsblpaWz8/Po2WVuNHh1fmBpZHIpqal9fOjizhTteY+RZ9rv02I/pq0W6QVH3pSncBz3m55r9ZIPycHfmenvxe4uyutIgfT5u4bgkDusl9gcF0rnfnz+b2NpSaQWBFeu8GIL1xQj5AH/6FAsEr/50F28e/gA9ny6KjLrxIp0TE+UucmQOl5AFNLXkzZufWamWHYEI39PEP2If97CMdm51N6DSmIekwAVmneXTBr0PVYx+aTgfQbU3p+R4jKHdRurBq0oEw6AKSfm+QDbpGF/w3VOP+oBnMHbqdx409FjP4RRHHkAj5IWgQiBUjHfMTuQ1Icpg5avI4sQVRu8EHdWptM1aKrIjuscfeL+kZwxBTYoElztOQ2UygjRIjEphaZsyWodHgvm9SC8QC/JygEA6DiCDeEMhAQFhhOpvxa/18A0TiYMahIy0L2hYIZWeYH9JR085Al4qts1re5St2/SR6DINBGEVYQCWOETHDMAHZ+pcZIQJGTV4RtMmg8UbhuWL1+VLLA2RFHYC71kiRo0SNpjwQh8pj2EFU3oTNmS1WqgIA') format('woff2');
	}

	.picker-icon {
		font-family: "mxdatepickericon" !important;
	}

	.picker-icon-you:before {
		content: "\e63e";
	}

	.picker-icon-zuo:before {
		content: "\e640";
	}

	.picker-icon-zuozuo:before {
		content: "\e641";
	}

	.picker-icon-youyou:before {
		content: "\e642";
	}
</style>
