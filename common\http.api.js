/**
 * Copyright (c) 2013-Now http://aidex.vip All rights reserved.
 */
// 此处第二个参数vm，就是我们在页面使用的this，你可以通过vm获取vuex等操作
const install = (app, vm) => {
	console.log('🔧 初始化 HTTP API...');

	// 获取全局属性
	const $u = app.config.globalProperties.$u;
	const store = app.config.globalProperties.$store;

	if (!$u) {
		console.warn('uView Plus 不可用，跳过 API 配置');
		return;
	}

	// 参数配置对象
	const config = store?.state?.vuex_config || {
		adminPath: '/admin' // 默认管理路径
	};

	console.log('📋 API 配置:', config);
	console.log('🔍 $u 对象:', $u);
	console.log('🌐 $u.http:', $u.http);
	console.log('📡 $u.http.get:', $u.http?.get);

	// 创建便捷的 HTTP 方法
	const httpGet = (url, params = {}) => {
		return $u.http.request({
			url,
			method: 'GET',
			data: params
		});
	};

	const httpPost = (url, params = {}) => {
		return $u.http.request({
			url,
			method: 'POST',
			data: params
		});
	};

	const httpPut = (url, params = {}) => {
		return $u.http.request({
			url,
			method: 'PUT',
			data: params
		});
	};

	const httpPostJson = (url, params = {}) => {
		return $u.http.request({
			url,
			method: 'POST',
			header: {
				'content-type': 'application/json'
			},
			data: params
		});
	};

	// 将各个定义的接口名称，统一放进对象挂载到$u.api下
	$u.api = {

		// 基础服务：登录登出、身份信息、菜单授权、切换系统、字典数据等
		lang: (params = {}) => httpGet('/lang/'+params.lang),
		index: (params = {}) => httpGet(config.adminPath+'/mobile/index', params),
		getUserInfo: (params = {}) => httpGet(config.adminPath+'/mobile/user/getUserInfo', params),
		login: (params = {}) => httpPost(config.adminPath+'/mobile/login/loginByPassword', params),
		// PDA登录接口
		pdaLogin: (params = {}) => httpPostJson(config.adminPath+'/login', params),
		pdaLogout: (params = {}) => httpPostJson(config.adminPath+'/logout', params),
		// 获取配布单列表
		getFpmArrangeOrderList: (params = {}) => httpGet(config.adminPath+'/product/fpmArrangeOrder/getFpmArrangeOrderList', params),
		// 获取成品配布单详情
		getFpmArrangeOrder: (params = {}) => httpGet('/product/fpmArrangeOrder/getFpmArrangeOrder', params),
		// 生成出仓单
		outFpmArrangeOrder: (params = {}) => httpPut('/product/fpmArrangeOrder/outFpmArrangeOrder', params),
		sendCode: (params = {}) => httpPost(config.adminPath+'/mobile/login/sendCode', params),
		registerUser: (params = {}) => httpPost(config.adminPath+'/mobile/user/registerUser', params),
		//首页相关api
		getIndexCardInfo: (params = {}) => httpGet(config.adminPath+'/mobile/index/getIndexCardInfo', params),
		getM2mOrderFlowList: (params = {}) => httpGet(config.adminPath+'/mobile/index/getM2mOrderFlowList', params),
		//获取卡可购买套餐包
		getM2mOrderPackageList: (params = {}) => httpGet(config.adminPath+'/mobile/index/getM2mOrderPackageList', params),

		logout: (params = {}) => httpGet(config.adminPath+'/mobile/login/logout', params),
		authInfo: (params = {}) => httpGet(config.adminPath+'/authInfo', params),
		menuTree: (params = {}) => httpGet(config.adminPath+'/menuTree', params),
		switchSys: (params = {}) => httpGet(config.adminPath+'/switch/'+params.sysCode),
		dictData: (params = {}) => httpGet(config.adminPath+'/system/dict/data/type/'+params.dictType),

		// 账号服务：验证码接口、忘记密码接口、注册账号接口等
		validCode: (params = {}) => $u.http.request({
			url: '/validCode',
			method: 'GET',
			dataType: 'text',
			data: params
		}),
		getFpValidCode: (params = {}) => httpPost('/account/getFpValidCode', params),
		savePwdByValidCode: (params = {}) => httpPost('/account/savePwdByValidCode', params),
		getRegValidCode: (params = {}) => httpPost('/account/getRegValidCode', params),
		saveRegByValidCode: (params = {}) => httpPost('/account/saveRegByValidCode', params),

		// APP公共服务
		upgradeCheck: () => httpPost('/app/upgrade/check', {appCode: config.appCode, appVersion: config.appVersion}),
		commentSave: (params = {}) => httpPost('/app/comment/save', params),

		// 个人信息修改
		user: {
			saveUserInfo: (params = {}) => httpPost(config.adminPath+'/mobile/user/saveUserInfo', params),
			infoSavePwd: (params = {}) => httpPut(config.adminPath+'/system/user/profile/updatePwd', params),
			infoSavePqa: (params = {}) => httpPost(config.adminPath+'/sys/user/infoSavePqa', params),
		},

		// 员工用户查询
		empUser: {
			listData: (params = {}) => httpGet(config.adminPath+'/sys/empUser/listData', params),
		},
		// 获取坯布其他出货单列表
		getGfmOtherDeliveryOrderList: (params = {}) => httpGet('/product/fpmArrangeOrder/getFpmArrangeOrderList', params),

		// 组织机构查询
		office: {
			treeData: (params = {}) => httpGet(config.adminPath+'/sys/office/treeData', params),
		},

		// 增删改查例子
		testData: {
			form: (params = {}) => httpPost(config.adminPath+'/test/testData/form', params),
			list: (params = {}) => httpPost(config.adminPath+'/test/testData/listData', params),
			save: (params = {}) => httpPostJson(config.adminPath+'/test/testData/save', params),
			disable: (params = {}) => httpPost(config.adminPath+'/test/testData/disable', params),
			enable: (params = {}) => httpPost(config.adminPath+'/test/testData/enable', params),
			delete: (params = {}) => httpPost(config.adminPath+'/test/testData/delete', params),
		},
		
	};
	
}

export default {
	install
}
