<template>
	<view class="wrap">
		<common-navbar title="我的" backgroundColor="#5b95ff" titleColor="#ffffff" :showBack="false">
		</common-navbar>
		<view class="header">
			<view class="userinfo">
				<view class="image" @click="navTo('info')">
					<!-- <image :src="avatarUrl"></image> -->
					<up-avatar fontSize="100" :src="avatarUrl"></up-avatar>
				</view>
				<view class="info" style="display: flex;justify-content: space-between;">
					<view>
						<view class="username">{{ loginUserName }}</view>
						<!--view class="usercode">普通员工</view-->
					</view>
					<view class="sign-in-images"><image src="/static/aidex/images/sign-in.png"></image></view>
				</view>
			</view>
		<!--
			<up-row class="userinfo-topbox" gutter="16" justify="center">
				<up-col span="4" text-align="center">
					<view class="number">9,999<em>元</em></view>
					<view>余额</view>
				</up-col>
				<up-col span="4" text-align="center"  @click="navTo('/pages/sys/application/recharge')">
					<view><up-icon size="28px" color="#ffffff" name="rmb-circle"></up-icon></view>
					<view>充值</view>
				</up-col>
				<up-col span="4" text-align="center" @click="navTo('/pages/sys/application/balance-details')">
					<view>
						<view class="iconfont icon-faan" style="font-size: 24px;color:#ffffff;"></view>
					</view>
					<view>余额明细</view>
				</up-col>
			</up-row>
			!-->
		</view>
		<view class="list">
			<view>
				<up-cell-group class="personal-list">
					<up-gap height="20" bgColor="#f5f5f5"></up-gap>
					<up-cell icon="question-circle" :iconStyle="{ color: '#ff8d06' }" title="常见问题" isLink @click="navTo('problem')"></up-cell>
					<up-cell icon="kefu-ermai" :iconStyle="{ color: '#5f8dff' }" title="意见反馈" isLink @click="navTo('comment')"></up-cell>
					<up-cell icon="map" :iconStyle="{ color: '#316ede' }" title="账号安全" isLink @click="navTo('pwd')"></up-cell>
					<up-cell icon="order" :iconStyle="{ color: '#59bdf9' }" title="清除缓存" isLink @click="navTo('clear-cache')"></up-cell>
					<!--<up-cell icon="account" :iconStyle="{ color: '#27c0dc' }" title="关于我们" isLink @click="navTo('about')"></up-cell>
					<up-cell icon="kefu-ermai" :iconStyle="{ color: '#ff8a00' }" title="联系客服" isLink @click="navTo('service')"></up-cell>!-->
					<up-gap height="20" bgColor="#f5f5f5"></up-gap>
					<up-cell icon="setting" :iconStyle="{ color: '#2767dc' }" title="系统设置" isLink @click="navTo('setting')"></up-cell>
				</up-cell-group>
			</view>
		</view>
	</view>
</template>
<script setup>
import { ref, computed, getCurrentInstance } from 'vue'
import CommonNavbar from "@/components/common-navbar/index"

/**
 * Copyright (c) 2013-Now http://aidex.vip All rights reserved.
 */

// 响应式数据
const loginUserName = ref('')

// 获取当前实例
const instance = getCurrentInstance()
const { proxy } = instance

// 计算属性
const avatarUrl = computed(() => {
	// let url = proxy.vuex_user.avatarUrl || '/ctxPath/static/images/user1.jpg';
	// url = url.replace('/ctxPath/', proxy.vuex_config.baseUrl + '/');
	let url = proxy?.vuex_config?.baseUrl + proxy?.vuex_user?.avatar || '/static/aidex/tabbar/my_2.png'
	url = proxy?.replaceAll(url, '\\', '/') || url
	return url + '?t=' + new Date().getTime()
})

// 页面生命周期 - 使用 onMounted 替代 onLoad
import { onMounted } from 'vue'
onMounted(() => {
	loginUserName.value = getApp().globalData.UserName
})

// 方法
const navTo = (url) => {
	uni.navigateTo({
		url: url
	})
}

const logout = () => {
	proxy?.$u?.api?.logout().then((res) => {
		proxy?.$u?.toast(res.msg)
		if (res.code == '200' || res.code == '401') {
			setTimeout(() => {
				uni.reLaunch({
					url: '/pages/sys/login/index'
				})
			}, 500)
		}
	})
}

const upgrade = () => {
	// #ifdef APP-PLUS
	proxy?.$u?.api?.upgradeCheck().then((res) => {
		if (res.result == 'true') {
			uni.showModal({
				title: '提示',
				content: res.message + '是否下载更新？',
				showCancel: true,
				success: function (res2) {
					if (res2.confirm) {
						plus.runtime.openURL(res.data.apkUrl)
					}
				}
			})
		} else {
			proxy?.$u?.toast(res.message)
		}
	})
	// #endif
	// #ifndef APP-PLUS
	proxy?.$u?.toast('小程序端或H5端无需检查更新')
	// #endif
}
</script>
<style lang="scss">
@import 'index.scss';
page {
	background-color: #f5f5f5;
}
.wrap .u-cell-box .u-cell_title{
	color:#202328;
}
.sign-in-images{
	width: 125rpx;
	height:50rpx;
	position: absolute;
	right: 0;
	top:5px;
	uni-image{
		width: 125rpx;
		height:50rpx;
	}
}
</style>
